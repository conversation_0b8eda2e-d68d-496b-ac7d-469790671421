import { useRef, useState, useEffect, useCallback, useMemo } from 'react';
import { createPortal } from 'react-dom';
import { Track } from 'livekit-client';

// Enhanced control components with better state management
const ControlButton = ({ 
  isActive, 
  onClick, 
  activeColor = '#4CAF50', 
  inactiveColor = '#f44336',
  children, 
  ariaLabel,
  disabled = false 
}) => (
  <button
    className={`pip-btn ${isActive ? 'active' : 'inactive'}`}
    onClick={onClick}
    aria-label={ariaLabel}
    disabled={disabled}
    style={{
      backgroundColor: isActive ? activeColor : inactiveColor,
      opacity: disabled ? 0.5 : 1,
      cursor: disabled ? 'not-allowed' : 'pointer'
    }}
  >
    {children}
  </button>
);

const MicButton = ({ room }) => {
  const [isMuted, setIsMuted] = useState(false);

  const updateState = useCallback(() => {
    const micTrack = room.localParticipant.getTrackPublication(Track.Source.Microphone);
    setIsMuted(micTrack?.isMuted ?? true);
  }, [room]);

  useEffect(() => {
    updateState();
    room.on('trackMuted', updateState);
    room.on('trackUnmuted', updateState);
    return () => {
      room.off('trackMuted', updateState);
      room.off('trackUnmuted', updateState);
    };
  }, [room, updateState]);

  const handleClick = useCallback(() => {
    const micTrack = room.localParticipant.getTrackPublication(Track.Source.Microphone);
    room.localParticipant.setMicrophoneEnabled(micTrack?.isMuted ?? true);
  }, [room]);

  return (
    <ControlButton
      isActive={!isMuted}
      onClick={handleClick}
      ariaLabel={isMuted ? "Unmute microphone" : "Mute microphone"}
    >
      <svg viewBox="0 0 24 24" aria-hidden="true">
        {isMuted ? (
          <path d="M19 11h-1.7c0 .74-.16 1.43-.43 2.05l1.23 1.23c.56-.98.9-2.09.9-3.28zm-4.02.17c0-.06.02-.11.02-.17V5c0-1.66-1.34-3-3-3S9 3.34 9 5v.18l5.98 5.99zM4.27 3L3 4.27l6.01 6.01V11c0 1.66 1.33 3 2.99 3 .22 0 .44-.03.65-.08l1.66 1.66c-.71.33-1.5.52-2.31.52-2.76 0-5.3-2.1-5.3-5.1H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c.91-.13 1.77-.45 2.54-.9L19.73 21 21 19.73 4.27 3z"/>
        ) : (
          <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z"/>
        )}
      </svg>
    </ControlButton>
  );
};

const CameraButton = ({ room, onToggle }) => {
  const [isMuted, setIsMuted] = useState(false);

  const updateState = useCallback(() => {
    const cameraTrack = room.localParticipant.getTrackPublication(Track.Source.Camera);
    setIsMuted(cameraTrack?.isMuted ?? true);
  }, [room]);

  useEffect(() => {
    updateState();
    room.on('trackMuted', updateState);
    room.on('trackUnmuted', updateState);
    return () => {
      room.off('trackMuted', updateState);
      room.off('trackUnmuted', updateState);
    };
  }, [room, updateState]);

  const handleClick = useCallback(() => {
    const cameraTrack = room.localParticipant.getTrackPublication(Track.Source.Camera);
    room.localParticipant.setCameraEnabled(cameraTrack?.isMuted ?? true);
    onToggle?.();
  }, [room, onToggle]);

  return (
    <ControlButton
      isActive={!isMuted}
      onClick={handleClick}
      ariaLabel={isMuted ? "Turn on camera" : "Turn off camera"}
    >
      <svg viewBox="0 0 24 24" aria-hidden="true">
        {isMuted ? (
          <path d="M21 6.5l-4 4V7c0-.55-.45-1-1-1H9.82L21 17.18V6.5zM3.27 2L2 3.27 4.73 6H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.21 0 .39-.08.54-.18L19.73 21 21 19.73 3.27 2zM5 16V8h1.73l8 8H5z"/>
        ) : (
          <path d="M17 10.5V7c0-.55-.45-1-1-1H4c-.55 0-1 .45-1 1v10c0 .55.45 1 1 1h12c.55 0 1-.45 1-1v-3.5l4 4v-11l-4 4z"/>
        )}
      </svg>
    </ControlButton>
  );
};

const ScreenShareButton = ({ room, meetingFeatures, onToggle }) => {
  const [isSharing, setIsSharing] = useState(false);

  const updateState = useCallback(() => {
    const screenTrack = room.localParticipant.getTrackPublication(Track.Source.ScreenShare);
    setIsSharing(screenTrack && !screenTrack.isMuted);
  }, [room]);

  useEffect(() => {
    updateState();
    room.on('trackPublished', updateState);
    room.on('trackUnpublished', updateState);
    return () => {
      room.off('trackPublished', updateState);
      room.off('trackUnpublished', updateState);
    };
  }, [room, updateState]);

  const handleClick = useCallback(async () => {
    try {
      const screenTrack = room.localParticipant.getTrackPublication(Track.Source.ScreenShare);
      await room.localParticipant.setScreenShareEnabled(!screenTrack || screenTrack.isMuted);
      onToggle?.();
    } catch (error) {
      console.error('Screen share error:', error);
    }
  }, [room, onToggle]);

  if (meetingFeatures?.screen_sharing !== 1) return null;

  return (
    <ControlButton
      isActive={isSharing}
      onClick={handleClick}
      activeColor="#4CAF50"
      inactiveColor="#FF9800"
      ariaLabel={isSharing ? "Stop screen sharing" : "Start screen sharing"}
    >
      <svg viewBox="0 0 24 24" aria-hidden="true">
        <path d="M20 18c1.1 0 1.99-.9 1.99-2L22 6c0-1.1-.9-2-2-2H4c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2H0v2h24v-2h-4zM4 6h16v10H4V6z"/>
      </svg>
    </ControlButton>
  );
};

const EndCallButton = ({ room, isElectronApp, onClose }) => {
  const handleClick = useCallback(() => {
    if (isElectronApp) {
      window?.electronAPI?.ipcRenderer?.send("stop-annotation");
    }
    room.disconnect();
    onClose();
  }, [room, isElectronApp, onClose]);

  return (
    <ControlButton
      isActive={false}
      onClick={handleClick}
      inactiveColor="#f44336"
      ariaLabel="End call"
    >
      <svg viewBox="0 0 24 24" aria-hidden="true">
        <path d="M12 9c-1.6 0-3.15.25-4.6.72v3.1c0 .39-.23.74-.56.9-.98.49-1.87 1.12-2.66 1.85-.18.18-.43.28-.7.28-.28 0-.53-.11-.71-.29L.29 13.08c-.18-.17-.29-.42-.29-.7 0-.28.11-.53.29-.71C3.34 8.78 7.46 7 12 7s8.66 1.78 11.71 4.67c.***********.29.71 0 .28-.11.53-.29.7l-2.48 2.48c-.18.18-.43.29-.71.29-.27 0-.52-.1-.7-.28-.79-.73-1.68-1.36-2.66-1.85-.33-.16-.56-.51-.56-.9v-3.1C15.15 9.25 13.6 9 12 9z"/>
      </svg>
    </ControlButton>
  );
};

// Enhanced layout components
const PipLayout = ({ title, children, controls }) => (
  <div className="pip-container">
    <div className="pip-header">{title}</div>
    <div className="pip-content">{children}</div>
    <div className="pip-controls">{controls}</div>
  </div>
);

const VideoDisplay = ({ track, className = "" }) => {
  const videoRef = useRef(null);

  useEffect(() => {
    if (videoRef.current && track) {
      const stream = new MediaStream([track.mediaStreamTrack]);
      videoRef.current.srcObject = stream;
      videoRef.current.play().catch(e => console.log('Video play error:', e));
    }
  }, [track]);

  return (
    <video
      ref={videoRef}
      className={`pip-video ${className}`}
      autoPlay
      playsInline
      muted
    />
  );
};

const AvatarDisplay = ({ name, avatarText, status, size = 80 }) => (
  <div className="pip-avatar-container">
    <div className="pip-avatar-inner">
      <div 
        className="pip-avatar-circle"
        style={{ width: size, height: size, fontSize: size * 0.4 }}
      >
        {avatarText}
      </div>
      <div className="pip-avatar-name">{name}</div>
      <div className="pip-avatar-status">{status}</div>
    </div>
  </div>
);

// Enhanced PiP hook with better React patterns
export const useEnhancedPictureInPicture = ({
  room,
  tracks,
  isTrackReference,
  generateAvatar,
  setIsPIPEnabled,
  setToastNotification,
  setToastStatus,
  setShowToast,
  meetingFeatures,
  isElectronApp,
  pipConfig = {}
}) => {
  const pipWindowRef = useRef(null);
  const pipContainerRef = useRef(null);
  const [pipWindowDocument, setPipWindowDocument] = useState(null);

  // Default configuration
  const defaultConfig = {
    width: 320,
    height: 300,
    title: "Daakia",
    resizable: false,
    ...pipConfig
  };

  // Check Document PiP support
  const isSupported = useMemo(() => {
    return 'documentPictureInPicture' in window;
  }, []);

  // Get current track to display
  const currentTrack = useMemo(() => {
    if (!tracks?.length) return null;

    // Priority 1: Screen share (any participant)
    const screenShareTracks = tracks
      .filter(isTrackReference)
      .filter((track) => track.publication.source === Track.Source.ScreenShare);

    if (screenShareTracks.length > 0 && screenShareTracks[0].publication.isSubscribed) {
      return screenShareTracks[0];
    }

    // Priority 2: Local camera
    const localCameraTracks = tracks
      .filter(isTrackReference)
      .filter((track) =>
        track.publication.source === Track.Source.Camera &&
        track.participant.isLocal
      );

    if (localCameraTracks.length > 0) {
      return localCameraTracks[0];
    }

    // Fallback to local participant for avatar
    return {
      participant: room.localParticipant,
      source: Track.Source.Camera,
      publication: null
    };
  }, [tracks, isTrackReference, room]);

  // PiP Content Component
  const PipContent = useCallback(() => {
    if (!currentTrack) return null;

    if (currentTrack.publication && currentTrack.publication.track && !currentTrack.publication.isMuted) {
      return <VideoDisplay track={currentTrack.publication.track} />;
    } else {
      const name = room.localParticipant.name || 'You';
      const avatarText = generateAvatar(name);
      const status = currentTrack.publication ? 'Camera Off' : 'No Camera';
      
      return (
        <AvatarDisplay
          name={name}
          avatarText={avatarText}
          status={status}
        />
      );
    }
  }, [currentTrack, room, generateAvatar]);

  // PiP Controls Component
  const PipControls = useCallback(() => (
    <>
      <MicButton room={room} />
      <CameraButton room={room} onToggle={() => {
        // Force re-render after a short delay to allow track state to update
        setTimeout(() => setPipWindowDocument(prev => prev), 100);
      }} />
      <ScreenShareButton 
        room={room} 
        meetingFeatures={meetingFeatures} 
        onToggle={() => {
          setTimeout(() => setPipWindowDocument(prev => prev), 100);
        }} 
      />
      <EndCallButton 
        room={room} 
        isElectronApp={isElectronApp} 
        onClose={closePipWindow} 
      />
    </>
  ), [room, meetingFeatures, isElectronApp]);

  // Enhanced styles with CSS custom properties and modern features
  const getPipStyles = useCallback(() => `
    :root {
      --pip-primary: #2196F3;
      --pip-success: #4CAF50;
      --pip-danger: #f44336;
      --pip-warning: #FF9800;
      --pip-dark: #1a1a1a;
      --pip-dark-light: #2c2c2c;
      --pip-text: #ffffff;
      --pip-text-muted: rgba(255, 255, 255, 0.7);
      --pip-border-radius: 8px;
      --pip-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
      --pip-transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    }

    * { 
      margin: 0; 
      padding: 0; 
      box-sizing: border-box; 
    }
    
    body { 
      background: var(--pip-dark); 
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
      overflow: hidden; 
      width: 100vw; 
      height: 100vh;
      color: var(--pip-text);
    }
    
    .pip-container { 
      width: 100%; 
      height: 100%; 
      position: relative; 
      display: flex; 
      flex-direction: column;
      background: var(--pip-dark);
      border-radius: var(--pip-border-radius);
      overflow: hidden;
      box-shadow: var(--pip-shadow);
    }
    
    .pip-header { 
      background: var(--pip-dark-light); 
      color: var(--pip-text); 
      padding: 12px 16px; 
      font-size: 14px; 
      font-weight: 600; 
      flex-shrink: 0; 
      z-index: 10;
      border-bottom: 1px solid rgba(255, 255, 255, 0.1);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .pip-content { 
      flex: 1; 
      position: relative; 
      overflow: hidden;
      display: flex;
      align-items: center;
      justify-content: center;
      background: var(--pip-dark);
    }
    
    .pip-video { 
      width: 100%; 
      height: 100%; 
      object-fit: contain; 
      background: #000;
      border-radius: 0;
    }
    
    .pip-avatar-container { 
      display: flex; 
      align-items: center; 
      justify-content: center; 
      height: 100%; 
      color: var(--pip-text); 
      text-align: center; 
      background: var(--pip-dark);
    }
    
    .pip-avatar-inner { 
      display: flex; 
      flex-direction: column; 
      align-items: center;
      animation: fadeIn 0.3s ease-out;
    }
    
    .pip-avatar-circle { 
      width: 80px; 
      height: 80px; 
      border-radius: 50%; 
      background: linear-gradient(135deg, var(--pip-primary), var(--pip-success)); 
      display: flex; 
      align-items: center; 
      justify-content: center; 
      font-size: 32px; 
      font-weight: bold; 
      margin-bottom: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
      transition: var(--pip-transition);
    }
    
    .pip-avatar-circle:hover {
      transform: scale(1.05);
    }
    
    .pip-avatar-name { 
      font-size: 14px; 
      margin-bottom: 4px; 
      font-weight: 500;
    }
    
    .pip-avatar-status { 
      font-size: 11px; 
      color: var(--pip-text-muted);
    }
    
    .pip-controls { 
      height: 64px; 
      background: rgba(0, 0, 0, 0.95); 
      display: flex; 
      align-items: center; 
      justify-content: center; 
      gap: 12px; 
      padding: 12px; 
      flex-shrink: 0; 
      z-index: 10;
      backdrop-filter: blur(10px);
      border-top: 1px solid rgba(255, 255, 255, 0.1);
    }
    
    .pip-btn { 
      width: 40px; 
      height: 40px; 
      border-radius: 50%; 
      border: none; 
      cursor: pointer; 
      display: flex; 
      align-items: center; 
      justify-content: center; 
      transition: var(--pip-transition); 
      color: var(--pip-text);
      position: relative;
      overflow: hidden;
    }
    
    .pip-btn::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      transform: scale(0);
      transition: transform 0.2s ease;
    }
    
    .pip-btn:hover::before {
      transform: scale(1);
    }
    
    .pip-btn:hover { 
      transform: scale(1.1);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
    
    .pip-btn:active { 
      transform: scale(0.95); 
    }
    
    .pip-btn:disabled {
      cursor: not-allowed;
      transform: none;
    }
    
    .pip-btn:disabled:hover {
      transform: none;
      box-shadow: none;
    }
    
    .pip-btn svg { 
      width: 18px; 
      height: 18px; 
      fill: currentColor;
      z-index: 1;
      position: relative;
    }

    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }

    @media (display-mode: picture-in-picture) {
      .pip-container {
        border-radius: 0;
      }
    }

    @media (max-width: 280px) {
      .pip-controls {
        gap: 8px;
        padding: 8px;
      }
      .pip-btn {
        width: 32px;
        height: 32px;
      }
      .pip-btn svg {
        width: 14px;
        height: 14px;
      }
    }
  `, []);

  // Open PiP window
  const openPipWindow = useCallback(async () => {
    if (!isSupported) {
      setToastNotification("Document Picture-in-Picture not supported");
      setToastStatus("error");
      setShowToast(true);
      return false;
    }

    if (pipWindowRef.current) {
      return true; // Already open
    }

    try {
      const pipWindow = await window.documentPictureInPicture.requestWindow({
        width: defaultConfig.width,
        height: defaultConfig.height,
      });

      pipWindowRef.current = pipWindow;
      setPipWindowDocument(pipWindow.document);
      setIsPIPEnabled(true);

      // Setup document
      const pipDoc = pipWindow.document;
      
      // Add styles
      const style = pipDoc.createElement('style');
      style.textContent = getPipStyles();
      pipDoc.head.appendChild(style);

      // Create container
      const container = pipDoc.createElement('div');
      container.id = 'pip-root';
      pipDoc.body.appendChild(container);
      pipContainerRef.current = container;

      // Handle window events
      const handlePageHide = () => {
        closePipWindow();
      };

      const handleResize = () => {
        // Handle responsive behavior if needed
        console.log('PiP window resized');
      };

      pipWindow.addEventListener('pagehide', handlePageHide);
      pipWindow.addEventListener('resize', handleResize);

      // Store cleanup functions
      pipWindow._cleanup = () => {
        pipWindow.removeEventListener('pagehide', handlePageHide);
        pipWindow.removeEventListener('resize', handleResize);
      };

      return true;
    } catch (error) {
      console.error('PiP error:', error);
      setToastNotification("Failed to create Picture-in-Picture");
      setToastStatus("error");
      setShowToast(true);
      return false;
    }
  }, [isSupported, defaultConfig, getPipStyles, setIsPIPEnabled, setToastNotification, setToastStatus, setShowToast]);

  // Close PiP window
  const closePipWindow = useCallback(() => {
    if (pipWindowRef.current) {
      pipWindowRef.current._cleanup?.();
      pipWindowRef.current.close();
      pipWindowRef.current = null;
    }
    setPipWindowDocument(null);
    pipContainerRef.current = null;
    setIsPIPEnabled(false);
  }, [setIsPIPEnabled]);

  // Toggle PiP mode
  const togglePipMode = useCallback(async (enabled) => {
    if (enabled) {
      return await openPipWindow();
    } else {
      closePipWindow();
      return true;
    }
  }, [openPipWindow, closePipWindow]);

  // Render PiP content using Portal
  const pipPortal = useMemo(() => {
    if (!pipWindowDocument || !pipContainerRef.current) return null;

    return createPortal(
      <PipLayout
        title={defaultConfig.title}
        controls={<PipControls />}
      >
        <PipContent />
      </PipLayout>,
      pipContainerRef.current
    );
  }, [pipWindowDocument, defaultConfig.title, PipControls, PipContent]);

  // Document Picture-in-Picture API information
  const apiInfo = useMemo(() => ({
    // Main API methods
    isSupported,
    requestWindow: () => window.documentPictureInPicture?.requestWindow,
    window: () => window.documentPictureInPicture?.window,
    
    // Events available
    events: ['enter', 'pagehide', 'resize'],
    
    // Window properties and methods
    windowMethods: [
      'close()', 'focus()', 'blur()', 'postMessage()',
      'addEventListener()', 'removeEventListener()'
    ],
    
    // CSS features
    cssFeatures: ['@media (display-mode: picture-in-picture)'],
    
    // Limitations
    limitations: [
      'Only one PiP window per tab',
      'Cannot be navigated',
      'Position cannot be set by website',
      'Must be same-origin',
      'Requires user activation',
      'Limited browser support (Chrome, Edge, Opera)'
    ],
    
    // Configuration options
    configOptions: [
      'width', 'height', 'disallowReturnToOpener'
    ]
  }), [isSupported]);

  return {
    // Core functionality
    togglePipMode,
    openPipWindow,
    closePipWindow,
    
    // State
    isPipOpen: !!pipWindowRef.current,
    pipWindow: pipWindowRef.current,
    pipDocument: pipWindowDocument,
    
    // Portal for React rendering
    pipPortal,
    
    // API information and capabilities
    apiInfo,
    
    // Current track being displayed
    currentTrack,
    
    // Support check
    isSupported
  };
};